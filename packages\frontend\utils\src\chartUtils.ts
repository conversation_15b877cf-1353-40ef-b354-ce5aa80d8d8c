export interface ChartDataPoint {
  x: Date | number | string;
  y: number;
  [key: string]: any;
}
 
export interface ChartDataset {
  label: string;
  color: string;
  data: ChartDataPoint[];
  [key: string]: any;
}
 
export interface ChartTransformOptions {
  maxDataPoints?: number;
  sortByTime?: boolean;
  filterInvalid?: boolean;
  groupBy?: string;
}
 
/**
 * Predefined color palettes for charts
 */
export const CHART_COLOR_PALETTES = {
  default: [
    '#3B82F6',
    '#EF4444',
    '#10B981',
    '#F59E0B',
    '#8B5CF6',
    '#EC4899',
    '#06B6D4',
    '#84CC16',
    '#F97316',
    '#6366F1',
  ],
  cybersecurity: [
    '#dc2626', // red for critical
    '#f59e0b', // amber for high
    '#eab308', // yellow for medium
    '#22c55e', // green for low
    '#3b82f6', // blue for info
  ],
  status: {
    success: '#22c55e',
    warning: '#f59e0b',
    danger: '#dc2626',
    info: '#3b82f6',
    secondary: '#6b7280',
  },
} as const;
 
/**
 * Gets an array of colors for charts based on count needed
 */
export function getChartColors(
  count: number,
  palette: 'default' | 'cybersecurity' = 'default',
): string[] {
  const colors = CHART_COLOR_PALETTES[palette];
  const result: string[] = [];
 
  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }
 
  return result;
}
 
/**
 * Gets color for status/severity levels
 */
export function getStatusColor(status: string): string {
  const normalized = status.toLowerCase();
 
  switch (normalized) {
    case 'critical':
    case 'high':
    case 'danger':
    case 'error':
      return CHART_COLOR_PALETTES.status.danger;
 
    case 'warning':
    case 'medium':
    case 'warn':
      return CHART_COLOR_PALETTES.status.warning;
 
    case 'success':
    case 'low':
    case 'ok':
    case 'healthy':
      return CHART_COLOR_PALETTES.status.success;
 
    case 'info':
    case 'information':
      return CHART_COLOR_PALETTES.status.info;
 
    default:
      return CHART_COLOR_PALETTES.status.secondary;
  }
}
 
/**
 * Transforms raw data into chart-ready format
 */
export function transformForChart<T extends Record<string, any>>(
  data: T[],
  options: ChartTransformOptions = {},
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
    groupBy,
  } = options;
 
  if (!data.length) return [];
 
  // Group data if groupBy is specified
  if (groupBy) {
    return transformGroupedData(data, groupBy, options);
  }
 
  // Simple transformation for single dataset
  const colors = getChartColors(1);
 
  const chartData = data
    .map((item, index) => ({
      x: item.timestamp ? new Date(item.timestamp) : new Date(),
      y: typeof item.value === 'number' ? item.value : 0,
      ...item,
    }))
    .filter((entry) => {
      if (!filterInvalid) return true;
      return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
    });
 
  if (sortByTime) {
    chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
  }
 
  // Limit data points
  const limitedData = chartData.slice(-maxDataPoints);
 
  return [
    {
      label: 'Data',
      color: colors[0],
      data: limitedData,
    },
  ];
}
 
/**
 * Transforms grouped data into multiple chart datasets
 */
function transformGroupedData<T extends Record<string, any>>(
  data: T[],
  groupBy: string,
  options: ChartTransformOptions,
): ChartDataset[] {
  const {
    maxDataPoints = 50,
    sortByTime = true,
    filterInvalid = true,
  } = options;
 
  // Group data by the specified field
  const groups = data.reduce(
    (acc, item) => {
      const groupKey = item[groupBy] || 'unknown';
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(item);
      return acc;
    },
    {} as Record<string, T[]>,
  );
 
  const colors = getChartColors(Object.keys(groups).length);
 
  return Object.entries(groups)
    .map(([groupKey, groupData], index) => {
      const chartData = groupData
        .map((item) => ({
          x: item.timestamp ? new Date(item.timestamp) : new Date(),
          y: typeof item.value === 'number' ? item.value : 0,
          ...item,
        }))
        .filter((entry) => {
          if (!filterInvalid) return true;
          return !isNaN(entry.x.getTime()) && !isNaN(entry.y);
        });
 
      if (sortByTime) {
        chartData.sort((a, b) => a.x.getTime() - b.x.getTime());
      }
 
      // Limit data points
      const limitedData = chartData.slice(-maxDataPoints);
 
      return {
        label: groupKey,
        color: colors[index],
        data: limitedData,
      };
    })
    .filter((dataset) => dataset.data.length > 0);
}
 
/**
 * Transforms deployment data specifically (matching the settings page pattern)
 * Handles both legacy format and modern format with timeseries/predictions
 * Creates individual charts for each deployment
 */
export function transformDeploymentData(
  deployments: Array<{
    name: string;
    namespace: string;
    data: Record<string, number> | {
      timeseries: Record<string, number>;
      predictions: {
        "1min": Record<string, number>;
      };
      alerts: number[];
    };
    display_name?: string;
  }>,
): Record<string, ChartDataset[]> {
  if (!deployments.length) return {};
 
  const colors = getChartColors(20);
  const blueColor = colors[0]; // Use the first color from the palette (typically blue)
  const result: Record<string, ChartDataset[]> = {};
 
  // Create individual chart for each deployment
  deployments.forEach((deployment) => {
    // Use deployment name as the key for individual charts
    const deploymentKey = `${deployment.name}-${deployment.namespace}`;
    const chartData: ChartDataset[] = [];
    // Check if this is the modern format with timeseries and predictions
    const isModernFormat = deployment.data &&
      typeof deployment.data === 'object' &&
      'timeseries' in deployment.data &&
      'predictions' in deployment.data;
 
    if (isModernFormat) {
      const modernData = deployment.data as {
        timeseries: Record<string, number>;
        predictions: {
          "1min": Record<string, number>;
        };
        alerts: number[];
      };
 
      // Process timeseries data (actual observed data)
      const timeseries = modernData.timeseries;
      if (timeseries && typeof timeseries === 'object') {
        const timeseriesKeys = Object.keys(timeseries);
        const timeseriesEntries = timeseriesKeys
          .map((timestamp) => {
            const value = timeseries[timestamp];
            const time = parseInt(timestamp);
            return {
              x: new Date(time),
              y: typeof value === 'number' ? value : 0,
            };
          })
          .filter((entry) => !isNaN(entry.x.getTime()) && !isNaN(entry.y))
          .sort((a, b) => a.x.getTime() - b.x.getTime());
 
        const limitedTimeseries = timeseriesEntries.slice(-60);
        if (limitedTimeseries.length > 0) {
          chartData.push({
            label: deployment.display_name || deployment.name,
            color: blueColor,
            data: limitedTimeseries,
          });
        }
      }
    } else {
      // Handle legacy format (direct data object)
      const legacyData = deployment.data as Record<string, number>;
      const dataKeys = Object.keys(legacyData);
      const dataEntries = dataKeys
        .map((timestamp) => {
          const value = legacyData[timestamp];
          const time = parseInt(timestamp);
          return {
            x: new Date(time),
            y: typeof value === 'number' ? value : 0,
          };
        })
        .filter((entry) => !isNaN(entry.x.getTime()) && !isNaN(entry.y))
        .sort((a, b) => a.x.getTime() - b.x.getTime());
 
      const limitedData = dataEntries.slice(-60);
      if (limitedData.length > 0) {
        chartData.push({
          label: deployment.display_name || deployment.name,
          color: blueColor,
          data: limitedData,
        });
      }
    }
 
    // Add this deployment's chart data to the result if it has data
    if (chartData.length > 0) {
      result[deploymentKey] = chartData;
    }
  });
 
  return result;
}
 